{"name": "warrior-support-system-backend", "version": "1.0.0", "description": "Backend for Warrior Support System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "cleanup-db": "node --no-deprecation scripts/cleanup-database.js", "init-db": "node --no-deprecation scripts/init-database.js", "reset-db": "npm run cleanup-db && npm run init-db", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "csv-parser": "^3.0.0", "json2csv": "^6.0.0-alpha.2", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["military", "support", "system", "nodejs", "mongodb"], "author": "Warrior Support System Team", "license": "MIT", "type": "commonjs"}