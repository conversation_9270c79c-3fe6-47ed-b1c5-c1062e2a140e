import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import '../styles/Financial_Assessment.css';

const Financial_Assessment = () => {
  const navigate = useNavigate();
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Parallel facial processing states
  const [facialProcessingActive, setFacialProcessingActive] = useState(false);
  const [facialResults, setFacialResults] = useState([]);
  const [currentProcessingMode, setCurrentProcessingMode] = useState('financial_stress');
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const facialSessionId = useRef(null);
  const processingIntervalRef = useRef(null);
  
  // Voice recording states
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [audioChunks, setAudioChunks] = useState([]);
  
  // Camera and microphone availability
  const [cameraAvailable, setCameraAvailable] = useState(true);
  const [microphoneAvailable, setMicrophoneAvailable] = useState(true);
  const [showCameraPopup, setShowCameraPopup] = useState(false);
  const [showMicPopup, setShowMicPopup] = useState(false);

  useEffect(() => {
    const fetchQuestions = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/financial_questionnaire', {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
        });
        setQuestions(response.data.questions);
        
        // Initialize facial processing session
        await initializeFacialProcessing();
        await initializeVoiceRecording();
      } catch (err) {
        console.error('Error loading financial questionnaire:', err);
        setError('Error loading questionnaire. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchQuestions();
  }, []);

  // Initialize facial processing
  const initializeFacialProcessing = async () => {
    try {
      // Generate unique session ID
      facialSessionId.current = `financial_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Start camera
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { width: 320, height: 240 } 
      });
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
        setFacialProcessingActive(true);
        startParallelFacialProcessing();
        setCameraAvailable(true);
      }
    } catch (error) {
      console.error('Error initializing facial processing:', error);
      setCameraAvailable(false);
      setShowCameraPopup(true);
      setTimeout(() => setShowCameraPopup(false), 3000);
    }
  };

  // Initialize voice recording
  const initializeVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      setMediaRecorder(recorder);
      setMicrophoneAvailable(true);
      
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          setAudioChunks(prev => [...prev, event.data]);
        }
      };
    } catch (error) {
      console.error('Error initializing voice recording:', error);
      setMicrophoneAvailable(false);
      setShowMicPopup(true);
      setTimeout(() => setShowMicPopup(false), 3000);
    }
  };

  // Start parallel facial processing
  const startParallelFacialProcessing = () => {
    processingIntervalRef.current = setInterval(() => {
      captureAndProcessFrame();
    }, 3000); // Capture frame every 3 seconds for financial assessment
  };

  // Capture and process facial frame
  const captureAndProcessFrame = async () => {
    if (!videoRef.current || !canvasRef.current || !facialProcessingActive) return;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    const context = canvas.getContext('2d');
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);
    
    // Convert to blob and send to backend
    canvas.toBlob(async (blob) => {
      if (blob) {
        await sendFrameToBackend(blob);
      }
    }, 'image/jpeg', 0.8);
  };

  // Send frame to backend for processing
  const sendFrameToBackend = async (blob) => {
    const formData = new FormData();
    formData.append('frame', blob);
    formData.append('session_id', facialSessionId.current);
    formData.append('processing_mode', currentProcessingMode);
    formData.append('question_index', currentQuestionIndex);
    formData.append('assessment_type', 'financial');

    try {
      const response = await axios.post('/api/parallel_facial_process', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      
      if (response.data.success) {
        setFacialResults(prev => [...prev, response.data.result]);
      }
    } catch (error) {
      console.error('Error processing facial frame:', error);
    }
  };

  // Start voice recording
  const startRecording = () => {
    if (mediaRecorder && microphoneAvailable) {
      setAudioChunks([]);
      mediaRecorder.start();
      setIsRecording(true);
    }
  };

  // Stop voice recording
  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  // Handle answer change
  const handleAnswerChange = (value) => {
    const currentQuestion = questions[currentQuestionIndex];
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: value
    }));
  };

  // Handle text input for open-ended questions
  const handleTextChange = (event) => {
    handleAnswerChange(event.target.value);
  };

  const handleNext = () => {
    const newIndex = currentQuestionIndex + 1;
    setCurrentQuestionIndex(newIndex);
    
    // Update processing mode based on question category
    if (questions[newIndex]) {
      const category = questions[newIndex].category || 'financial_stress';
      setCurrentProcessingMode(category);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // Stop facial processing
    if (processingIntervalRef.current) {
      clearInterval(processingIntervalRef.current);
    }
    setFacialProcessingActive(false);
    
    // Stop camera
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
    }

    // Generate final assessment
    await generateFinalFinancialAssessment();
    navigate("/financial-assessment-results");
  };

  // Generate final financial assessment
  const generateFinalFinancialAssessment = async () => {
    try {
      const assessmentData = {
        session_id: facialSessionId.current,
        answers: answers,
        facial_results: facialResults,
        processing_modes: currentProcessingMode,
        assessment_type: 'financial'
      };

      await axios.post('/api/generate_financial_assessment', assessmentData);
    } catch (error) {
      console.error('Error generating financial assessment:', error);
    }
  };

  if (loading) {
    return (
      <div className="financial-assessment-loading">
        <div className="loading-spinner"></div>
        <p>Loading Financial Assessment...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="financial-assessment-error">
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Retry</button>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;

  return (
    <div className="financial-assessment-container">
      {/* Camera/Microphone Availability Popups */}
      {showCameraPopup && (
        <div className="availability-popup camera-popup">
          <div className="popup-content">
            <span className="popup-icon">📷</span>
            <p>Camera not available</p>
            <small>Facial analysis will be disabled</small>
          </div>
        </div>
      )}
      
      {showMicPopup && (
        <div className="availability-popup mic-popup">
          <div className="popup-content">
            <span className="popup-icon">🎤</span>
            <p>Microphone not available</p>
            <small>Voice recording will be disabled</small>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="assessment-header">
        <h1>Financial Stress Assessment</h1>
        <div className="progress-container">
          <div className="progress-bar">
            <div className="progress-fill" style={{ width: `${progress}%` }}></div>
          </div>
          <span className="progress-text">
            Question {currentQuestionIndex + 1} of {questions.length}
          </span>
        </div>
      </div>

      <div className="assessment-layout">
        {/* Left side - Question Interface */}
        <div className="question-section">
          <div className="question-container">
            <div className="question-number">
              Question {currentQuestionIndex + 1}
            </div>
            
            <div className="question-text">
              {currentQuestion?.text?.english || currentQuestion?.text}
            </div>

            <div className="answer-section">
              {currentQuestion?.type === 'scale' || currentQuestion?.type === 'multiple_choice' ? (
                <div className="options-container">
                  {currentQuestion.options?.english?.map((option, index) => (
                    <label key={index} className="option-label">
                      <input
                        type="radio"
                        name={`question-${currentQuestion.id}`}
                        value={index}
                        checked={answers[currentQuestion.id] === index}
                        onChange={() => handleAnswerChange(index)}
                      />
                      <span className="option-text">{option}</span>
                    </label>
                  ))}
                </div>
              ) : (
                <div className="text-input-container">
                  <textarea
                    value={answers[currentQuestion.id] || ''}
                    onChange={handleTextChange}
                    placeholder="Please provide your detailed response..."
                    rows={4}
                    className="text-input"
                  />
                  
                  {microphoneAvailable && (
                    <div className="voice-recording">
                      <button
                        type="button"
                        className={`record-button ${isRecording ? 'recording' : ''}`}
                        onClick={isRecording ? stopRecording : startRecording}
                      >
                        {isRecording ? '🔴 Stop Recording' : '🎤 Record Response'}
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="navigation-buttons">
            {currentQuestionIndex < questions.length - 1 ? (
              <button 
                onClick={handleNext} 
                disabled={isSubmitting || !answers[currentQuestion?.id]}
                className="next-button"
              >
                Next Question
              </button>
            ) : (
              <button 
                onClick={handleSubmit} 
                disabled={isSubmitting || !answers[currentQuestion?.id]}
                className="submit-button"
              >
                {isSubmitting ? 'Submitting...' : 'Complete Assessment'}
              </button>
            )}
          </div>
        </div>

        {/* Right side - AI Processing Panel */}
        <div className="ai-processing-section">
          <div className="processing-panel">
            <h3>AI Financial Stress Analysis</h3>
            
            {/* Camera Feed */}
            <div className="camera-container">
              <video 
                ref={videoRef} 
                autoPlay 
                muted 
                className="facial-video"
                style={{ 
                  width: '280px', 
                  height: '210px', 
                  border: cameraAvailable ? '2px solid #28a745' : '2px solid #dc3545',
                  borderRadius: '8px'
                }}
              />
              <canvas 
                ref={canvasRef} 
                style={{ display: 'none' }}
              />
            </div>
            
            {/* Processing Status */}
            <div className="processing-status">
              <div className={`status-indicator ${facialProcessingActive ? 'active' : 'inactive'}`}>
                {facialProcessingActive ? '🟢 Analysis Active' : '🔴 Analysis Inactive'}
              </div>
              <div className="processing-mode">
                Focus: <span className="mode-label">{currentProcessingMode.replace('_', ' ')}</span>
              </div>
              <div className="frames-processed">
                Frames Analyzed: {facialResults.length}
              </div>
            </div>

            {/* Recent Analysis */}
            {facialResults.length > 0 && (
              <div className="recent-analysis">
                <h4>Recent Stress Indicators</h4>
                <div className="analysis-summary">
                  {facialResults.slice(-3).map((result, index) => (
                    <div key={index} className="analysis-item">
                      <span className="timestamp">
                        {new Date(result.timestamp).toLocaleTimeString()}
                      </span>
                      <span className="stress-level">
                        Stress: {result.stress_score ? Math.round(result.stress_score * 100) : 'N/A'}%
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* System Status */}
            <div className="system-status">
              <div className={`status-item ${cameraAvailable ? 'available' : 'unavailable'}`}>
                📷 Camera: {cameraAvailable ? 'Available' : 'Unavailable'}
              </div>
              <div className={`status-item ${microphoneAvailable ? 'available' : 'unavailable'}`}>
                🎤 Microphone: {microphoneAvailable ? 'Available' : 'Unavailable'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Financial_Assessment;
