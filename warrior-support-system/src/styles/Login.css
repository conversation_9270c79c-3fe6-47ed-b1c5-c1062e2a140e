.login-container {
  min-height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
              url('/src/assets/mountain-background.jpg') center/cover;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  max-width: 500px;
  width: 100%;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 30px;
  text-align: center;
}

.logo-section {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.logo-placeholder {
  width: 70px !important;
  height: 70px !important;
  background: rgba(255, 255, 255, 0.2);
  
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.logo1{
  background-image: url('../images/logo1.jpg') !important;
}

.logo2{
  background-image: url('../images/logo2.jpg') !important;
}

.login-header h1 {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 10px 0;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.login-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

.login-form-container {
  padding: 30px;
}

.login-form h2 {
  text-align: center;
  color: #2c3e50 !important;
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8px;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 1px;
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #bdc3c7;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fff;
}

.form-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 10px rgba(52, 152, 219, 0.2);
}

.error-message {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(231, 76, 60, 0.3);
  margin-bottom: 20px;
  text-align: center;
  font-weight: bold;
}

.login-btn {
  width: 100%;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: none;
  padding: 15px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 30px;
}

.login-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #229954, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
}

.login-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
}

.demo-section {
  border-top: 2px solid #ecf0f1;
  padding-top: 25px;
  margin-top: 25px;
}

.demo-section h3 {
  color: #2c3e50;
  font-size: 18px;
  margin-bottom: 10px;
  text-align: center;
}

.demo-section p {
  color: #7f8c8d;
  text-align: center;
  margin-bottom: 20px;
  font-size: 14px;
}

.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.demo-btn {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 15px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 14px;
}

.demo-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.demo-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.co-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.jso-btn {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.user-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.role-icon {
  font-size: 20px;
  width: 30px;
  text-align: center;
}

.demo-btn div {
  flex: 1;
}

.demo-btn strong {
  display: block;
  font-size: 14px;
  margin-bottom: 2px;
}

.demo-btn small {
  display: block;
  opacity: 0.8;
  font-size: 12px;
}

.login-footer {
  text-align: center;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #ecf0f1;
}

.login-footer p {
  color: #7f8c8d;
  margin-bottom: 15px;
}

.register-link {
  color: #3498db !important;
  text-decoration: none;
  font-weight: bold;
}

.register-link:hover {
  text-decoration: underline;
}

.system-info {
  color: #95a5a6;
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: 10px;
  }
  
  .login-content {
    max-width: 100%;
  }
  
  .login-header {
    padding: 20px;
  }
  
  .login-header h1 {
    font-size: 20px;
  }
  
  .login-form-container {
    padding: 20px;
  }
  
  .demo-buttons {
    gap: 8px;
  }
  
  .demo-btn {
    padding: 10px 12px;
  }
}