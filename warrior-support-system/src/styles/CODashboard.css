/* CO Dashboard Styles */
.co-dashboard {
  min-height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
              url('/src/assets/mountain-bg.jpg') center/cover no-repeat;
  background-attachment: fixed;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

.co-dashboard-header {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  border-bottom: 3px solid #2c5530;
  position: sticky;
  top: 0;
  z-index: 100;
}

.co-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.co-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.co-title {
  color: #ffffff;
  font-size: 2rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  margin: 0;
}

.co-subtitle {
  color: #a8d5a8;
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

.co-user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #ffffff;
}

.co-user-details {
  text-align: right;
}

.co-user-name {
  font-weight: bold;
  font-size: 1.1rem;
  margin: 0;
}

.co-user-role {
  font-size: 0.9rem;
  color: #a8d5a8;
  margin: 0;
}

.co-logout-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.co-logout-btn:hover {
  background: #c82333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.co-main-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.co-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.co-stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.co-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.co-stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.co-stat-title {
  font-size: 0.9rem;
  color: #666;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.co-stat-icon {
  width: 24px;
  height: 24px;
  color: #2c5530;
}

.co-stat-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c5530;
  margin: 0;
}

.co-stat-description {
  font-size: 0.85rem;
  color: #888;
  margin-top: 0.5rem;
}

.co-actions-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.co-section-title {
  font-size: 1.5rem;
  color: #2c5530;
  margin-bottom: 1.5rem;
  font-weight: bold;
  border-bottom: 2px solid #2c5530;
  padding-bottom: 0.5rem;
}

.co-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.co-action-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #2c5530;
  transition: all 0.3s ease;
}

.co-action-card:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.co-action-title {
  font-size: 1.1rem;
  font-weight: bold;
  color: #2c5530;
  margin-bottom: 0.5rem;
}

.co-action-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.co-action-btn {
  background: #2c5530;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  width: 100%;
}

.co-action-btn:hover {
  background: #1e3a21;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(44, 85, 48, 0.3);
}

.co-action-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.co-recent-activity {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.co-activity-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.co-activity-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  transition: background-color 0.3s ease;
}

.co-activity-item:hover {
  background: #f8f9fa;
}

.co-activity-item:last-child {
  border-bottom: none;
}

.co-activity-icon {
  width: 40px;
  height: 40px;
  background: #2c5530;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
}

.co-activity-content {
  flex: 1;
}

.co-activity-title {
  font-weight: bold;
  color: #333;
  margin: 0 0 0.25rem 0;
}

.co-activity-description {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.co-activity-time {
  color: #999;
  font-size: 0.8rem;
  white-space: nowrap;
}

.co-battalion-management {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.co-battalion-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.co-battalion-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #ddd;
  transition: all 0.3s ease;
}

.co-battalion-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.co-battalion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.co-battalion-name {
  font-weight: bold;
  color: #2c5530;
  margin: 0;
}

.co-battalion-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.co-battalion-status.approved {
  background: #d4edda;
  color: #155724;
}

.co-battalion-status.pending {
  background: #fff3cd;
  color: #856404;
}

.co-battalion-status.rejected {
  background: #f8d7da;
  color: #721c24;
}

.co-battalion-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.co-battalion-actions {
  display: flex;
  gap: 0.5rem;
}

.co-btn-approve {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.3s ease;
}

.co-btn-approve:hover {
  background: #218838;
}

.co-btn-reject {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.3s ease;
}

.co-btn-reject:hover {
  background: #c82333;
}

.co-btn-view {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.3s ease;
}

.co-btn-view:hover {
  background: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .co-dashboard-header {
    padding: 1rem;
  }
  
  .co-header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .co-title {
    font-size: 1.5rem;
  }
  
  .co-main-content {
    padding: 1rem;
  }
  
  .co-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .co-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .co-battalion-grid {
    grid-template-columns: 1fr;
  }
}

/* Loading States */
.co-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.co-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2c5530;
  border-radius: 50%;
  animation: co-spin 1s linear infinite;
}

@keyframes co-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notification Styles */
.co-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  z-index: 1000;
  animation: co-slideIn 0.3s ease-out;
}

.co-notification.success {
  background: #28a745;
}

.co-notification.error {
  background: #dc3545;
}

.co-notification.warning {
  background: #ffc107;
  color: #333;
}

@keyframes co-slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}