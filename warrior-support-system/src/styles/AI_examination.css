/* ===== Updated AI Examination Styles ===== */
.ai-examination-container {
  background: linear-gradient(
    rgba(47, 228, 37, 0.7),
    rgba(0, 146, 244, 0.3)
  );
  min-height: 50vh;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding:10px;
}

.question-title{
  background-color: white;
  padding: 10px;;
  border-radius: 5px;
}
.ai-examination-title {
  color: #3498db;
  font-size: 28px;
  text-align: center;
  font-weight: bold;
  letter-spacing: 1px;
  margin-bottom: 20px;
  text-transform: uppercase;
}

.progress-bar-container {
  width: 80%;
  margin: 20px auto;
  background: #ecf0f1;
  border-radius: 8px;
  overflow: hidden;
  height: 10px;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  transition: width 0.3s ease;
}

.voice-input-section {
  background: rgba(255, 255, 255, 0.97);
  border-radius: 14px;
  box-shadow: 0 4px 32px rgba(52, 152, 219, 0.12);
  padding: 20px;
  max-width: 200px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.manual-input-section {
  margin-top: 20px;
}

textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #bdc3c7;
  border-radius: 8px;
  background: #f7fafc;
  color: #2c3e50;
  font-size: 15px;
  resize: vertical;
}

textarea:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 8px rgba(52, 152, 219, 0.17);
}

.navigation-buttons {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

button {
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 8px;
  border: none;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  box-shadow: 0 2px 14px rgba(52, 152, 219, 0.09);
  transition: all 0.22s cubic-bezier(0.4, 0.6, 0.7, 1.1);
}

button.start {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

button.start:hover:not(:disabled) {
  background: linear-gradient(135deg, #229954, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(39, 174, 96, 0.25);
}

button.stop {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

button.stop:hover:not(:disabled) {
  background: linear-gradient(135deg, #c0392b, #e74c3c);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(231, 76, 60, 0.17);
}

button.next {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

button.next:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9, #3498db);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(52, 152, 219, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
  .voice-input-section {
    padding: 16px;
  }

  .ai-examination-title {
    font-size: 22px;
  }

  .navigation-buttons {
    flex-direction: column;
    gap: 10px;
  }
}

/* ===== end AI Examination Styles ===== */
