.peer-evaluation-container {
  min-height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
              url('/src/assets/mountain-background.jpg') center/cover;
  display: flex;
  flex-direction: column;
}

.evaluation-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.personnel-info {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  text-align: center;
}

.personnel-info h3 {
  color: #2c3e50;
  font-size: 24px;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.info-details {
  display: flex;
  justify-content: center;
  gap: 30px;
  color: #7f8c8d;
  font-weight: bold;
}

.question-progress {
  text-align: center;
  margin-bottom: 20px;
  color: white;
  font-size: 16px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.progress-bar {
  width: 100%;
  max-width: 600px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  margin: 10px auto;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #e67e22;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.evaluation-content h2 {
  color: white;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  margin: 20px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  text-transform: uppercase;
}

.question-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 900px;
  margin: 0 auto;
}

.question-section h3 {
  font-size: 18px;
  color: #2c3e50;
  margin-bottom: 25px;
  line-height: 1.5;
  padding: 15px;
  background: rgba(230, 126, 34, 0.1);
  border-left: 4px solid #e67e22;
  border-radius: 0 5px 5px 0;
}

.options {
  margin-bottom: 30px;
}

.option-label {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  cursor: pointer;
  padding: 12px;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.option-label:hover {
  background: rgba(230, 126, 34, 0.05);
}

.option-label input[type="radio"] {
  width: 18px;
  height: 18px;
  margin-right: 12px;
  margin-top: 2px;
  cursor: pointer;
  accent-color: #e67e22;
}

.option-text {
  font-size: 16px;
  line-height: 1.4;
  color: #2c3e50;
  flex: 1;
}

.question-section textarea {
  width: 100%;
  min-height: 120px;
  padding: 15px;
  font-size: 16px;
  border: 2px solid #bdc3c7;
  border-radius: 5px;
  resize: vertical;
  font-family: inherit;
  line-height: 1.5;
  margin-bottom: 20px;
  transition: border-color 0.3s ease;
}

.question-section textarea:focus {
  outline: none;
  border-color: #e67e22;
  box-shadow: 0 0 5px rgba(230, 126, 34, 0.3);
}

.navigation-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.prev-btn,
.next-btn {
  padding: 15px 30px;
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  text-transform: uppercase;
  transition: all 0.3s ease;
  min-width: 120px;
}

.prev-btn {
  background: #95a5a6;
  color: white;
}

.prev-btn:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
}

.next-btn {
  background: #e67e22;
  color: white;
  margin-left: auto;
}

.next-btn:hover {
  background: #d35400;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .evaluation-content {
    padding: 10px;
  }
  
  .personnel-info h3 {
    font-size: 20px;
  }
  
  .info-details {
    flex-direction: column;
    gap: 10px;
  }
  
  .question-section {
    padding: 20px;
  }
  
  .navigation-buttons {
    flex-direction: column;
  }
  
  .prev-btn,
  .next-btn {
    width: 100%;
    margin-left: 0;
  }
}