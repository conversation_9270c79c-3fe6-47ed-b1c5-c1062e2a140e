.instructions-container {
  min-height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
              url('/src/assets/mountain-background.jpg') center/cover;
  display: flex;
  flex-direction: column;
}

.instructions-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  overflow-y: auto;
}

.instructions-content h2 {
  color: white;
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  margin: 20px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  text-transform: uppercase;
}

.instructions-box {
  background: rgba(255, 255, 255, 0.95);
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  margin: 20px;
}

.instructions-box h3 {
  color: #2c3e50;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px;
  text-transform: uppercase;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.instruction-list {
  margin-bottom: 30px;
}

.instruction-list p {
  font-size: 16px;
  line-height: 1.6;
  color: #2c3e50;
  margin-bottom: 15px;
  padding: 10px;
  background: rgba(52, 152, 219, 0.05);
  border-left: 4px solid #3498db;
  border-radius: 0 5px 5px 0;
}

.agreement-section {
  background: rgba(46, 204, 113, 0.1);
  padding: 20px;
  border-radius: 8px;
  border: 2px solid #2ecc71;
  margin-bottom: 30px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  color: #27ae60;
  text-transform: uppercase;
}

.checkbox-container input[type="checkbox"] {
  width: 20px;
  height: 20px;
  margin-right: 15px;
  cursor: pointer;
  accent-color: #27ae60;
}

.proceed-btn {
  display: block;
  width: 200px;
  margin: 0 auto;
  padding: 15px 30px;
  font-size: 18px;
  font-weight: bold;
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  text-transform: uppercase;
  transition: all 0.3s ease;
}

.proceed-btn:hover:not(:disabled) {
  background: #229954;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
}

.proceed-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .instructions-content h2 {
    font-size: 24px;
  }
  
  .instructions-box {
    padding: 20px;
    margin: 10px;
  }
  
  .instructions-box h3 {
    font-size: 18px;
  }
  
  .instruction-list p {
    font-size: 14px;
    padding: 8px;
  }
  
  .checkbox-container {
    font-size: 14px;
  }
  
  .proceed-btn {
    width: 100%;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .instructions-box {
    padding: 15px;
  }
  
  .checkbox-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .checkbox-container input[type="checkbox"] {
    margin-right: 0;
  }
}