/* Add Data Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
  animation: modalOverlayFadeIn 0.3s ease-out;
}

@keyframes modalOverlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 2px solid #2c3e50;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  border-radius: 10px 10px 0 0;
  border-bottom: 3px solid #1a252f;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  font-weight: bold;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.close-btn:focus {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* Form Styles */
form {
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: bold;
  color: #2c3e50;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.25rem;
  position: relative;
}

.form-group label::after {
  content: '*';
  color: #e74c3c;
  margin-left: 0.25rem;
  font-size: 1.1rem;
}

.form-group input,
.form-group select {
  padding: 0.75rem 1rem;
  border: 2px solid #bdc3c7;
  border-radius: 8px;
  font-size: 0.9rem;
  font-family: inherit;
  background: white;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2), inset 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.form-group input:valid {
  border-color: #27ae60;
}

.form-group input:invalid:not(:placeholder-shown) {
  border-color: #e74c3c;
}

.form-group select {
  cursor: pointer;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  padding-right: 3rem;
  appearance: none;
}

.form-group select:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233498db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* Input type specific styles */
input[type="date"] {
  position: relative;
  color: #2c3e50;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  background: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232c3e50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3e%3c/rect%3e%3cline x1='16' y1='2' x2='16' y2='6'%3e%3c/line%3e%3cline x1='8' y1='2' x2='8' y2='6'%3e%3c/line%3e%3cline x1='3' y1='10' x2='21' y2='10'%3e%3c/line%3e%3c/svg%3e") no-repeat;
  background-size: 1.2rem;
  width: 1.2rem;
  height: 1.2rem;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

/* Submit Button */
.submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #229954, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.submit-btn:hover::before {
  left: 100%;
}

.submit-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(39, 174, 96, 0.3);
}

.submit-btn:focus {
  outline: 3px solid rgba(39, 174, 96, 0.5);
  outline-offset: 2px;
}

.submit-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-btn:disabled::before {
  display: none;
}

/* Form Validation Styles */
.form-group.error input,
.form-group.error select {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2);
}

.form-group.success input,
.form-group.success select {
  border-color: #27ae60;
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.2);
}

.error-message {
  color: #e74c3c;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.error-message::before {
  content: '⚠';
  font-size: 0.9rem;
}

.success-message {
  color: #27ae60;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.success-message::before {
  content: '✓';
  font-size: 0.9rem;
}

/* Loading State */
.form-loading {
  position: relative;
  pointer-events: none;
}

.form-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.form-loading .submit-btn {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-content {
    max-height: 95vh;
    border-radius: 8px;
  }

  .modal-header {
    padding: 1rem 1.5rem;
    border-radius: 6px 6px 0 0;
  }

  .modal-header h2 {
    font-size: 1.2rem;
    letter-spacing: 1px;
  }

  .close-btn {
    width: 35px;
    height: 35px;
    font-size: 1.5rem;
  }

  form {
    padding: 1.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-group input,
  .form-group select {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
  }

  .submit-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: 0.8rem 1rem;
  }

  .modal-header h2 {
    font-size: 1rem;
  }

  .close-btn {
    width: 30px;
    height: 30px;
    font-size: 1.2rem;
  }

  form {
    padding: 1rem;
  }

  .form-group label {
    font-size: 0.8rem;
  }

  .form-group input,
  .form-group select {
    padding: 0.5rem 0.7rem;
    font-size: 0.8rem;
  }

  .submit-btn {
    padding: 0.7rem 1rem;
    font-size: 0.9rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .modal-content {
    border: 3px solid #000;
  }

  .modal-header {
    background: #000;
    border-bottom: 3px solid #000;
  }

  .form-group input,
  .form-group select {
    border: 2px solid #000;
  }

  .submit-btn {
    background: #000;
    border: 2px solid #000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .modal-overlay,
  .modal-content,
  .close-btn,
  .form-group input,
  .form-group select,
  .submit-btn {
    animation: none;
    transition: none;
  }

  .submit-btn::before {
    display: none;
  }
}

/* Print Styles */
@media print {
  .modal-overlay {
    position: static;
    background: none;
    backdrop-filter: none;
  }

  .modal-content {
    box-shadow: none;
    border: 1px solid #000;
    max-height: none;
    overflow: visible;
  }

  .modal-header {
    background: #f0f0f0 !important;
    color: #000 !important;
  }

  .close-btn {
    display: none;
  }

  .submit-btn {
    display: none;
  }

  form {
    background: none !important;
  }
}

/* Focus Management */
.modal-content:focus {
  outline: none;
}

/* Accessibility Improvements */
.form-group input[aria-invalid="true"],
.form-group select[aria-invalid="true"] {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2);
}

.form-group input[aria-describedby],
.form-group select[aria-describedby] {
  margin-bottom: 0.25rem;
}

/* Custom Scrollbar for Modal */
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Placeholder Styling */
.form-group input::placeholder {
  color: #7f8c8d;
  opacity: 0.7;
  font-style: italic;
}

.form-group input:focus::placeholder {
  opacity: 0.5;
}

/* Form Field Icons */
.form-group.has-icon {
  position: relative;
}

.form-group.has-icon input {
  padding-left: 2.5rem;
}

.form-group.has-icon::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 2.2rem;
  width: 1rem;
  height: 1rem;
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.5;
  z-index: 1;
}

/* Specific field icons */
.form-group[data-field="rank"]::before {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232c3e50' stroke-width='2'%3e%3cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3e%3c/svg%3e");
}

.form-group[data-field="name"]::before {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232c3e50' stroke-width='2'%3e%3cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'/%3e%3ccircle cx='12' cy='7' r='4'/%3e%3c/svg%3e");
}

.form-group[data-field="armyNo"]::before {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232c3e50' stroke-width='2'%3e%3crect x='1' y='3' width='15' height='13'/%3e%3cpath d='M16 8l2-2 2 2'/%3e%3cpath d='M20 6v4'/%3e%3c/svg%3e");
}
