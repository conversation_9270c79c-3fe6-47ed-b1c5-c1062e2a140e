.examination-complete-container {
  min-height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
              url('/src/assets/mountain-background.jpg') center/cover;
  display: flex;
  flex-direction: column;
}

.complete-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.complete-modal {
  background: rgba(255, 255, 255, 0.95);
  padding: 50px;
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  text-align: center;
  min-width: 400px;
  animation: modalSlideIn 0.5s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.success-icon {
  margin-bottom: 30px;
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.checkmark {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-size: 40px;
  color: white;
  font-weight: bold;
  box-shadow: 0 4px 20px rgba(39, 174, 96, 0.4);
  position: relative;
  overflow: hidden;
}

.checkmark::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.complete-modal h2 {
  color: #2c3e50;
  font-size: 32px;
  font-weight: bold;
  margin: 30px 0;
  text-transform: uppercase;
  letter-spacing: 2px;
  line-height: 1.2;
}

.ok-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 15px 50px;
  font-size: 18px;
  font-weight: bold;
  border-radius: 8px;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
  position: relative;
  overflow: hidden;
}

.ok-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.ok-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.ok-btn:hover::before {
  left: 100%;
}

.ok-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

/* Success message animation */
.success-message {
  background: rgba(39, 174, 96, 0.1);
  border: 2px solid #27ae60;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  color: #27ae60;
  font-weight: bold;
  animation: fadeInUp 0.5s ease-out 0.3s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Confetti effect */
.confetti {
  position: absolute;
  width: 10px;
  height: 10px;
  background: #f39c12;
  animation: confetti-fall 3s linear infinite;
}

.confetti:nth-child(1) { left: 10%; animation-delay: 0s; background: #e74c3c; }
.confetti:nth-child(2) { left: 20%; animation-delay: 0.5s; background: #3498db; }
.confetti:nth-child(3) { left: 30%; animation-delay: 1s; background: #2ecc71; }
.confetti:nth-child(4) { left: 40%; animation-delay: 1.5s; background: #f39c12; }
.confetti:nth-child(5) { left: 50%; animation-delay: 2s; background: #9b59b6; }
.confetti:nth-child(6) { left: 60%; animation-delay: 0.3s; background: #e67e22; }
.confetti:nth-child(7) { left: 70%; animation-delay: 0.8s; background: #1abc9c; }
.confetti:nth-child(8) { left: 80%; animation-delay: 1.3s; background: #34495e; }
.confetti:nth-child(9) { left: 90%; animation-delay: 1.8s; background: #e91e63; }

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .complete-modal {
    min-width: 300px;
    padding: 40px 30px;
  }
  
  .complete-modal h2 {
    font-size: 24px;
    margin: 20px 0;
  }
  
  .checkmark {
    width: 60px;
    height: 60px;
    font-size: 30px;
  }
  
  .ok-btn {
    width: 100%;
    font-size: 16px;
    padding: 12px 30px;
  }
}

@media (max-width: 480px) {
  .complete-modal {
    padding: 30px 20px;
    margin: 20px;
  }
  
  .complete-modal h2 {
    font-size: 20px;
    letter-spacing: 1px;
  }
  
  .checkmark {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }
}