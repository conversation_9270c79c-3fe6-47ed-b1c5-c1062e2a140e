.register-container {
  min-height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url("/src/assets/mountain-background.jpg") center
    / cover;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  max-width: 500px;
  width: 100%;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.register-header {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 40px 30px;
  text-align: center;
}

.logo-section {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 25px;
}

.logo-placeholder {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.logo-placeholder:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.register-header h1 {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 15px 0;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.register-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  font-weight: 500;
}

.register-form-container {
  padding: 40px;
}

.register-form h2 {
  text-align: center;
  color: #2c3e50 !important;
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 40px;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
}

.register-form h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-radius: 2px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  margin-bottom: 25px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 1px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 15px 18px;
  border: 2px solid #bdc3c7;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fff;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 15px rgba(52, 152, 219, 0.2);
  transform: translateY(-1px);
}

.form-group select {
  
  cursor: pointer;
  
  padding-right: 3rem;
  appearance: none;
}

.form-group select:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233498db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.form-group small {
  display: block;
  color: #7f8c8d;
  font-size: 13px;
  margin-top: 8px;
  font-style: italic;
  line-height: 1.4;
}

.role-description {
  color: #27ae60 !important;
  font-weight: bold !important;
  background: rgba(39, 174, 96, 0.1);
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #27ae60;
  margin-top: 10px !important;
  font-style: normal !important;
}

.error-message {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid rgba(231, 76, 60, 0.3);
  margin-bottom: 25px;
  text-align: center;
  font-weight: bold;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.register-btn {
  width: 100%;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: none;
  padding: 18px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 30px;
  position: relative;
  overflow: hidden;
}

.register-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.register-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #229954, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.register-btn:hover::before {
  left: 100%;
}

.register-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.register-btn:disabled::before {
  display: none;
}

.register-footer {
  text-align: center;
  margin-top: 35px;
  padding-top: 25px;
  border-top: 1px solid #ecf0f1;
}

.register-footer p {
  color: #7f8c8d;
  margin-bottom: 20px;
  font-size: 16px;
}

.login-link {
  color: #3498db!important;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s ease;
}

.login-link:hover {
  color: #2980b9;
  text-decoration: underline;
}

.system-info {
  color: #95a5a6;
  font-size: 13px;
}

/* Input validation styles */
.form-group input:valid {
  border-color: #27ae60;
}

.form-group input:invalid:not(:placeholder-shown) {
  border-color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 768px) {
  .register-container {
    padding: 15px;
  }

  .register-content {
    max-width: 100%;
  }

  .register-header {
    padding: 30px 20px;
  }

  .register-header h1 {
    font-size: 24px;
  }

  .logo-placeholder {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .register-form-container {
    padding: 30px 25px;
  }

  .register-form h2 {
    font-size: 28px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .form-group {
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .register-container {
    padding: 10px;
  }

  .register-header {
    padding: 25px 15px;
  }

  .register-header h1 {
    font-size: 20px;
    letter-spacing: 1px;
  }

  .logo-section {
    gap: 20px;
    margin-bottom: 20px;
  }

  .logo-placeholder {
    width: 45px;
    height: 45px;
    font-size: 20px;
  }

  .register-form-container {
    padding: 25px 20px;
  }

  .register-form h2 {
    font-size: 24px;
    margin-bottom: 30px;
  }

  .form-group input,
  .form-group select {
    padding: 12px 15px;
    font-size: 15px;
  }

  .register-btn {
    padding: 15px;
    font-size: 16px;
  }
}

/* Loading state */
.register-form.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .register-content {
    border: 3px solid #000;
  }

  .form-group input,
  .form-group select {
    border: 2px solid #000;
  }

  .register-btn {
    background: #000;
    border: 2px solid #000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .register-content,
  .form-group input,
  .form-group select,
  .register-btn,
  .logo-placeholder {
    animation: none;
    transition: none;
  }

  .register-btn::before {
    display: none;
  }
}
