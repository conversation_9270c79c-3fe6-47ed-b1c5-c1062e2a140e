/* DataTable Styles */
.datatable-container {
  min-height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
              url('../assets/mountain-bg.jpg') center/cover no-repeat;
  background-attachment: fixed;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

.datatable-content {
  margin-top: 50px;
  padding: 2rem;
  background-color: white;
  max-width: 1400px;
  margin: 0 auto;
}

.datatable-header {
  
  text-align: center;
  margin-bottom: 2rem;
  align-items: center;
}

.datatable-title {
  color: #ffffff;
  font-size: 2.5rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.datatable-subtitle {
  color: #000000;
  font-size: 1.8rem;
  border: #000 5px double;
  font-weight: 600;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.datatable-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.datatable-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  min-width: 120px;
}

.datatable-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.datatable-btn-add {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.datatable-btn-add:hover {
  background: linear-gradient(135deg, #229954, #27ae60);
}

.datatable-btn-save {
  background: linear-gradient(135deg, #3498db, #5dade2);
  color: white;
}

.datatable-btn-save:hover {
  background: linear-gradient(135deg, #2980b9, #3498db);
}

.datatable-btn-remove {
  background: linear-gradient(135deg, #e74c3c, #ec7063);
  color: white;
}

.datatable-btn-remove:hover {
  background: linear-gradient(135deg, #c0392b, #e74c3c);
}

.datatable-btn-import {
  background: linear-gradient(135deg, #f39c12, #f7dc6f);
  color: white;
}

.datatable-btn-import:hover {
  background: linear-gradient(135deg, #e67e22, #f39c12);
}

.datatable-btn-export {
  background: linear-gradient(135deg, #9b59b6, #bb8fce);
  color: white;
}

.datatable-btn-export:hover {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
}

.datatable-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 1rem;
}

.datatable-scroll {
  overflow-x: auto;
  max-height: 70vh;
  overflow-y: auto;
}

.datatable-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.datatable-table th {
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  padding: 1rem 0.75rem;
  text-align: center;
  font-weight: bold;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 2px solid #1a252f;
}

.datatable-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #ecf0f1;
  vertical-align: middle;
  text-align: center;
}

.datatable-table tr:hover {
  background: rgba(52, 152, 219, 0.1);
}

.datatable-table tr:nth-child(even) {
  background: rgba(0, 0, 0, 0.02);
}

.datatable-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.datatable-status.completed {
  background: linear-gradient(135deg, #d5f4e6, #a8e6cf);
  color: #27ae60;
  border: 1px solid #27ae60;
}

.datatable-status.not-attempted {
  background: linear-gradient(135deg, #fadbd8, #f1948a);
  color: #e74c3c;
  border: 1px solid #e74c3c;
}

.datatable-status.evaluated {
  background: linear-gradient(135deg, #d6eaf8, #44e87a);
  color: #2a7e4f;
  border: 1px solid #3498db;
}

.datatable-btn-small {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.3s ease;
}

.datatable-btn-review {
  background: linear-gradient(135deg, #b65959, #d30f0f);
  color: white;
}


.datatable-actions-cell {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.datatable-btn-icon {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.datatable-btn-edit {
  background: linear-gradient(135deg, #f39c12, #f7dc6f);
}

.datatable-btn-edit:hover {
  background: linear-gradient(135deg, #e67e22, #f39c12);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

.datatable-btn-delete {
  background: linear-gradient(135deg, #e74c3c, #ec7063);
}

.datatable-btn-delete:hover {
  background: linear-gradient(135deg, #c0392b, #e74c3c);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.datatable-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
}

.datatable-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: datatable-spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes datatable-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.datatable-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #7f8c8d;
  text-align: center;
}

.datatable-empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.datatable-empty h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
}

.datatable-empty p {
  margin: 0;
  color: #7f8c8d;
}

.datatable-footer {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.datatable-count {
  font-weight: bold;
  color: #2c3e50;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .datatable-table {
    font-size: 0.8rem;
  }
  
  .datatable-table th,
  .datatable-table td {
    padding: 0.5rem 0.25rem;
  }
}

@media (max-width: 768px) {
  .datatable-content {
    padding: 1rem;
  }
  
  .datatable-title {
    font-size: 1.8rem;
  }
  
  .datatable-subtitle {
    font-size: 1rem;
  }
  
  .datatable-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .datatable-btn {
    width: 100%;
    max-width: 200px;
  }
  
  .datatable-table {
    font-size: 0.7rem;
  }
  
  .datatable-table th,
  .datatable-table td {
    padding: 0.5rem 0.25rem;
  }
  
  .datatable-actions-cell {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .datatable-btn-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .datatable-scroll {
    max-height: 60vh;
  }
  
  .datatable-table th,
  .datatable-table td {
    padding: 0.25rem;
    font-size: 0.65rem;
  }
  
  .datatable-status {
    font-size: 0.6rem;
    padding: 2px 6px;
    min-width: 60px;
  }
  
  .datatable-btn-small {
    padding: 4px 8px;
    font-size: 0.6rem;
  }
}

/* Print Styles */
@media print {
  .datatable-container {
    background: white !important;
  }
  
  .datatable-actions,
  .datatable-btn-icon,
  .datatable-btn-small {
    display: none !important;
  }
  
  .datatable-table {
    font-size: 0.7rem;
  }
  
  .datatable-table th,
  .datatable-table td {
    padding: 0.25rem;
    border: 1px solid #000;
  }
}

/* Scrollbar Styling */
.datatable-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.datatable-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.datatable-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.datatable-scroll::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation for table rows */
.datatable-table tbody tr {
  animation: datatable-fadeIn 0.3s ease-in;
}

@keyframes datatable-fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}