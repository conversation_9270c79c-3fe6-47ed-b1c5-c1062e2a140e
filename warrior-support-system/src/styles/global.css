/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

/* Common Button Styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-primary {
  background: #4a90e2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #357abd;
  transform: translateY(-2px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-2px);
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c0392b;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
  transform: translateY(-2px);
}

/* Common Input Styles */
.form-input {
  padding: 15px;
  border: none;
  border-radius: 5px;
  background: rgba(255,255,255,0.9);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  background: rgba(255,255,255,1);
  box-shadow: 0 0 10px rgba(74, 144, 226, 0.3);
}

.form-select {
  padding: 15px;
  border: none;
  border-radius: 5px;
  background: rgba(255,255,255,0.9);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-select:focus {
  outline: none;
  background: rgba(255,255,255,1);
  box-shadow: 0 0 10px rgba(74, 144, 226, 0.3);
}

/* Message Styles */
.error-message {
  color: #ff6b6b;
  text-align: center;
  margin-top: 1rem;
  padding: 10px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 5px;
  border: 1px solid rgba(255, 107, 107, 0.3);
  animation: shake 0.5s ease-in-out;
}

.success-message {
  color: #51cf66;
  text-align: center;
  margin-top: 1rem;
  padding: 10px;
  background: rgba(81, 207, 102, 0.1);
  border-radius: 5px;
  border: 1px solid rgba(81, 207, 102, 0.3);
  animation: fadeIn 0.5s ease-in-out;
}

/* Animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
  
  .form-input,
  .form-select {
    padding: 12px;
    font-size: 0.9rem;
  }
}