/* ===== Facial Examination Styles ===== */
.facial-examination-container {
  background: linear-gradient(
    rgba(44, 62, 80, 0.7),
    rgba(52, 152, 219, 0.3)
  );
  min-height: 100vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 20px 0;
}

.facial-examination-card {
  background: rgba(255, 255, 255, 0.97);
  border-radius: 14px;
  box-shadow: 0 4px 32px rgba(52, 152, 219, 0.12);
  padding: 40px 32px 28px 32px;
  max-width: 700px;
  width: 100%;
  margin: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.facial-examination-title {
  color: #3498db;
  font-size: 26px;
  text-align: center;
  font-weight: bold;
  letter-spacing: 1px;
  margin-bottom: 0.5em;
  text-shadow: 2px 2px 8px rgba(44, 62, 80, 0.07);
  text-transform: uppercase;
}

.facial-examination-divider {
  border: none;
  border-top: 2px solid #ecf0f1;
  margin: 0 0 24px 0;
}

.facial-examination-label {
  font-size: 13px;
  font-weight: bold;
  color: #2980b9;
  margin-bottom: 4px;
  text-transform: uppercase;
  display: block;
}

.facial-camera-select {
  width: 100%;
  padding: 13px 12px;
  font-size: 15px;
  border: 2px solid #bdc3c7;
  border-radius: 8px;
  background: #f7fafc;
  color: #2c3e50;
  margin-bottom: 15px;
  transition: border-color 0.3s;
}

.facial-camera-select:disabled {
  background: #ecf0f1;
  color: #b2bec3;
}

/* Video Container */
.video-container {
  position: relative;
  background: #2c3e50;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(44, 62, 80, 0.15);
}

.video-preview {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
}

/* Camera Controls */
.camera-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.camera-controls button {
  flex: 1;
  min-width: 140px;
}

/* Buttons */
.facial-examination-btn {
  padding: 15px 20px;
  font-size: 16px;
  border-radius: 8px;
  border: none;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  box-shadow: 0 2px 14px rgba(52, 152, 219, 0.09);
  transition: all 0.22s cubic-bezier(0.4,0.6,0.7,1.1);
}

.facial-examination-btn.start {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
}

.facial-examination-btn.start:hover:not(:disabled) {
  background: linear-gradient(135deg, #229954, #27ae60);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(39, 174, 96, .25);
}

.facial-examination-btn.capture {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.facial-examination-btn.capture:hover:not(:disabled) {
  background: linear-gradient(135deg, #e67e22, #f39c12);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(243, 156, 18, .25);
}

.facial-examination-btn.stop {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.facial-examination-btn.stop:hover:not(:disabled) {
  background: linear-gradient(135deg, #c0392b, #e74c3c);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(231, 76, 60, .17);
}

.facial-examination-btn.analyze {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
  width: 100%;
}

.facial-examination-btn.analyze:hover:not(:disabled) {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(155, 89, 182, .25);
}

.facial-examination-btn.next {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.facial-examination-btn.next:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9, #3498db);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(52, 152, 219, .25);
}

.facial-examination-btn.back {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;
}

.facial-examination-btn.back:hover:not(:disabled) {
  background: linear-gradient(135deg, #7f8c8d, #95a5a6);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(149, 165, 166, .25);
}

.facial-examination-btn:disabled {
  background: #bdc3c7;
  color: #fafafa;
  cursor: not-allowed;
  box-shadow: none;
}

/* Captured Images Section */
.captured-images-section {
  margin-top: 20px;
}

.section-title {
  color: #2c3e50;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  text-align: center;
}

.captured-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.captured-image-item {
  position: relative;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(44, 62, 80, 0.1);
}

.captured-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.image-timestamp {
  padding: 8px;
  font-size: 12px;
  color: #7f8c8d;
  text-align: center;
  background: #f8f9fa;
}

.remove-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(231, 76, 60, 0.8);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-image-btn:hover {
  background: rgba(231, 76, 60, 1);
}

/* Analysis Results */
.analysis-results {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-top: 16px;
}

.analysis-status {
  margin-top: 16px;
}

.streaming-indicator {
  background: rgba(231, 76, 60, 0.09);
  color: #e74c3c;
  border-radius: 8px;
  text-align: center;
  padding: 10px 0 8px 0;
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 0;
  border: 1.5px solid rgba(231, 76, 60, 0.12);
  letter-spacing: 1.5px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  transition: width 0.3s ease;
}

.result-item {
  margin-bottom: 12px;
  font-size: 16px;
  color: #2c3e50;
}

.emotions-breakdown {
  margin-top: 20px;
}

.emotions-breakdown h4 {
  color: #2c3e50;
  margin-bottom: 16px;
}

.emotion-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.emotion-label {
  min-width: 80px;
  font-weight: bold;
  color: #34495e;
  font-size: 14px;
}

.emotion-bar {
  flex: 1;
  height: 20px;
  background: #ecf0f1;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.emotion-fill {
  height: 100%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  transition: width 0.5s ease;
}

.emotion-value {
  min-width: 40px;
  text-align: right;
  font-weight: bold;
  color: #2c3e50;
  font-size: 14px;
}

.recommendations {
  margin-top: 20px;
}

.recommendations h4 {
  color: #2c3e50;
  margin-bottom: 12px;
}

.recommendations ul {
  padding-left: 20px;
}

.recommendations li {
  margin-bottom: 8px;
  color: #34495e;
  line-height: 1.5;
}

/* Navigation Buttons */
.navigation-buttons {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.navigation-buttons button {
  flex: 1;
}

/* Warning */
.facial-examination-warning {
  background: rgba(243, 156, 18, 0.11);
  color: #e67e22;
  border: 1.5px solid #f39c1260;
  padding: 13px;
  border-radius: 7px;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
  box-shadow: 0 2px 10px rgba(243, 156, 18, 0.07);
}

/* Figure Stress */
.figureStress {
  background-color: grey;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  width: 100%;
  height: auto;
  overflow: hidden;
}

.figureStress img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border: 2px solid #ccc;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .facial-examination-card {
    padding: 20px 16px;
    margin: 10px;
  }
  
  .facial-examination-title {
    font-size: 20px;
  }
  
  .camera-controls {
    flex-direction: column;
  }
  
  .camera-controls button {
    min-width: auto;
  }
  
  .captured-images-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
  
  .navigation-buttons {
    flex-direction: column;
  }
  
  .emotion-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .emotion-label {
    min-width: auto;
  }
  
  .emotion-value {
    text-align: left;
    min-width: auto;
  }
}

/* ===== End Facial Examination Styles ===== */
