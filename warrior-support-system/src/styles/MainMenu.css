/* Main Menu Component Styles */
.main-menu-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  position: relative;
}

.main-menu-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/background.jpg') center/cover;
  opacity: 0.3;
  z-index: 0;
}

.menu-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 80px);
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.menu-buttons {
  display: flex;
  gap: 4rem;
  animation: fadeIn 1s ease-out;
}

.menu-btn {
  width: 250px;
  height: 150px;
  font-size: 1.4rem;
  font-weight: bold;
  color: white;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  position: relative;
  overflow: hidden;
}

.menu-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.menu-btn:hover::before {
  left: 100%;
}

.start-examination {
  background: linear-gradient(135deg, #4a90e2, #357abd);
  animation: slideIn 0.8s ease-out;
}

.start-examination:hover {
  background: linear-gradient(135deg, #357abd, #2968a3);
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 40px rgba(74, 144, 226, 0.4);
}

.view-data {
  background: linear-gradient(135deg, #28a745, #218838);
  animation: slideIn 0.8s ease-out 0.2s both;
}

.view-data:hover {
  background: linear-gradient(135deg, #218838, #1e7e34);
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 40px rgba(40, 167, 69, 0.4);
}

.menu-btn:active {
  transform: translateY(-2px) scale(1.02);
}

/* Icon styles for menu buttons */
.menu-btn::after {
  content: '';
  position: absolute;
  top: 20px;
  right: 20px;
  width: 30px;
  height: 30px;
  background: rgba(255,255,255,0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.start-examination::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'/%3E%3C/svg%3E");
  background-size: 20px;
  background-repeat: no-repeat;
  background-position: center;
}

.view-data::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M9 17H7v-2h2v2zm0-4H7v-2h2v2zm0-4H7V7h2v2zm4 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V7h2v2zm4 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V7h2v2z'/%3E%3C/svg%3E");
  background-size: 20px;
  background-repeat: no-repeat;
  background-position: center;
}

.menu-btn:hover::after {
  transform: scale(1.2);
  background-color: rgba(255,255,255,0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .menu-buttons {
    flex-direction: column;
    gap: 2rem;
  }
  
  .menu-btn {
    width: 200px;
    height: 120px;
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .menu-content {
    padding: 1rem;
  }
  
  .menu-btn {
    width: 180px;
    height: 100px;
    font-size: 1rem;
  }
  
  .menu-buttons {
    gap: 1.5rem;
  }
}