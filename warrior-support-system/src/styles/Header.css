/* Header Component Styles */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: slideIn 0.6s ease-out;
}

.header-left h1 {
  font-size: 1.5rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 1px;
}
.header-logo-placeholder {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
  border: 2px solid rgba(255,255,255,0.3);
  transition: all 0.3s ease;
  overflow: hidden; /* ensures image doesn't spill out */
}

.header-logo-placeholder:hover {
  transform: scale(1.1);
  border-color: rgba(255,255,255,0.5);
}


.logo1{
  background-image: url('../images/logo1.jpg') !important;
  background-size: cover;
}

.logo2{
  background-image: url('../images/logo2.jpg') !important;
  background-size: cover;
}


.header-logo-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* ensures it fills the circle */
  border-radius: 50%; /* optional, in case you want circular cropping */
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: slideIn 0.6s ease-out 0.2s both;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  color: white;
  margin-right: 1rem;
}

.user-name {
  font-weight: bold;
  font-size: 0.9rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.user-role {
  font-size: 0.8rem;
  opacity: 0.8;
  color: #4a90e2;
  font-weight: 500;
}

.profile-logo-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.6rem;
  font-weight: bold;
  border: 2px solid rgba(255,255,255,0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.profile-logo-placeholder:hover {
  transform: scale(1.1);
  border-color: rgba(255,255,255,0.5);
}

.logout-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 0.8rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.logout-btn:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 0.8rem 1rem;
  }
  
  .header-left h1 {
    font-size: 1.2rem;
  }
  
  .user-info {
    display: none;
  }
  
  .header-logo-placeholder {
    width: 40px;
    height: 40px;
    font-size: 0.6rem;
  }
  
  .profile-logo-placeholder {
    width: 35px;
    height: 35px;
    font-size: 0.5rem;
  }
  
  .logout-btn {
    padding: 6px 12px;
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .header-left {
    gap: 0.5rem;
  }
  
  .header-left h1 {
    font-size: 1rem;
  }
  
  .header-right {
    gap: 0.5rem;
  }
}